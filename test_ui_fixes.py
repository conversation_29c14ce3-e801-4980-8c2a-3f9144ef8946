#!/usr/bin/env python3
"""
测试脚本：验证UI修复是否有效
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_functionality():
    """测试UI功能是否正常工作"""
    try:
        # 导入修复后的UI模块
        from automation_ui import AutomationApp
        
        print("✅ 成功导入 AutomationApp")
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建应用实例
        app = AutomationApp(root)
        print("✅ 成功创建 AutomationApp 实例")
        
        # 测试捕获模板功能（应该显示错误消息而不是崩溃）
        try:
            app.capture_template()
            print("✅ 捕获模板功能正常处理缺失依赖")
        except Exception as e:
            print(f"❌ 捕获模板功能出错: {e}")
        
        # 测试加载模板功能（应该显示错误消息而不是崩溃）
        try:
            app.load_template_from_file()
            print("✅ 加载模板功能正常处理缺失依赖")
        except Exception as e:
            print(f"❌ 加载模板功能出错: {e}")
        
        # 测试自动化功能（应该显示错误消息而不是崩溃）
        try:
            app.start_automation()
            print("✅ 自动化功能正常处理缺失依赖")
        except Exception as e:
            print(f"❌ 自动化功能出错: {e}")
        
        # 测试工作流功能（应该显示错误消息而不是崩溃）
        try:
            app.run_workflow()
            print("✅ 工作流功能正常处理缺失依赖")
        except Exception as e:
            print(f"❌ 工作流功能出错: {e}")
        
        # 清理
        root.destroy()
        print("✅ 测试完成，应用正常关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试UI修复...")
    print("=" * 50)
    
    success = test_ui_functionality()
    
    print("=" * 50)
    if success:
        print("🎉 所有测试通过！UI修复成功。")
        print("\n现在可以安全地使用应用程序，即使缺少某些依赖库也不会崩溃。")
        print("\n要获得完整功能，请安装以下库：")
        print("  pip install pillow opencv-python pynput")
    else:
        print("💥 测试失败！需要进一步修复。")
    
    return success

if __name__ == "__main__":
    main()
