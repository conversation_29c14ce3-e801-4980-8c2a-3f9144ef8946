#!/usr/bin/env python3
"""
按钮样式演示程序
展示新的现代化按钮样式
"""

import tkinter as tk
from tkinter import ttk

def create_button_demo():
    """创建按钮样式演示窗口"""
    root = tk.Tk()
    root.title("现代化按钮样式演示")
    root.geometry("800x600")
    
    # 设置样式
    style = ttk.Style()
    
    # 使用现代主题
    available_themes = style.theme_names()
    if 'vista' in available_themes:
        style.theme_use('vista')
    elif 'winnative' in available_themes:
        style.theme_use('winnative')
    else:
        style.theme_use('clam')
    
    # 定义现代化按钮样式
    # Plain button style (white background, dark text)
    style.configure("Plain.TButton",
                   padding=(12, 8),
                   relief="solid",
                   background='white',
                   foreground='#374151',
                   borderwidth=1,
                   focuscolor='none',
                   font=('Segoe UI', 9))

    style.map("Plain.TButton",
             background=[('active', '#F9FAFB'),
                       ('pressed', '#F3F4F6'),
                       ('disabled', '#F9FAFB')],
             foreground=[('disabled', '#9CA3AF')],
             bordercolor=[('focus', '#3B82F6'),
                        ('!focus', '#D1D5DB'),
                        ('disabled', '#E5E7EB')])

    # Primary button style (blue background, white text)
    style.configure("Primary.TButton",
                   padding=(12, 8),
                   relief="solid",
                   background='#3B82F6',
                   foreground='white',
                   borderwidth=1,
                   focuscolor='none',
                   font=('Segoe UI', 9))

    style.map("Primary.TButton",
             background=[('active', '#2563EB'),
                       ('pressed', '#1D4ED8'),
                       ('disabled', '#E5E7EB')],
             foreground=[('disabled', '#9CA3AF')],
             bordercolor=[('disabled', '#D1D5DB')])

    # Success button style (green background, white text)
    style.configure("Success.TButton",
                   padding=(12, 8),
                   relief="solid",
                   background='#10B981',
                   foreground='white',
                   borderwidth=1,
                   focuscolor='none',
                   font=('Segoe UI', 9))

    style.map("Success.TButton",
             background=[('active', '#059669'),
                       ('pressed', '#047857'),
                       ('disabled', '#E5E7EB')],
             foreground=[('disabled', '#9CA3AF')],
             bordercolor=[('disabled', '#D1D5DB')])

    # Info button style (gray background, white text)
    style.configure("Info.TButton",
                   padding=(12, 8),
                   relief="solid",
                   background='#6B7280',
                   foreground='white',
                   borderwidth=1,
                   focuscolor='none',
                   font=('Segoe UI', 9))

    style.map("Info.TButton",
             background=[('active', '#4B5563'),
                       ('pressed', '#374151'),
                       ('disabled', '#E5E7EB')],
             foreground=[('disabled', '#9CA3AF')],
             bordercolor=[('disabled', '#D1D5DB')])

    # Warning button style (orange background, white text)
    style.configure("Warning.TButton",
                   padding=(12, 8),
                   relief="solid",
                   background='#F59E0B',
                   foreground='white',
                   borderwidth=1,
                   focuscolor='none',
                   font=('Segoe UI', 9))

    style.map("Warning.TButton",
             background=[('active', '#D97706'),
                       ('pressed', '#B45309'),
                       ('disabled', '#E5E7EB')],
             foreground=[('disabled', '#9CA3AF')],
             bordercolor=[('disabled', '#D1D5DB')])

    # Danger button style (red background, white text)
    style.configure("Danger.TButton",
                   padding=(12, 8),
                   relief="solid",
                   background='#EF4444',
                   foreground='white',
                   borderwidth=1,
                   focuscolor='none',
                   font=('Segoe UI', 9))

    style.map("Danger.TButton",
             background=[('active', '#DC2626'),
                       ('pressed', '#B91C1C'),
                       ('disabled', '#E5E7EB')],
             foreground=[('disabled', '#9CA3AF')],
             bordercolor=[('disabled', '#D1D5DB')])

    # 创建主容器
    main_frame = ttk.Frame(root, padding=30)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="🎨 现代化按钮样式演示", 
                           font=('Segoe UI', 16, 'bold'))
    title_label.pack(pady=(0, 20))
    
    # 说明文字
    desc_label = ttk.Label(main_frame, 
                          text="以下是根据您提供的设计参考实现的6种现代化按钮样式：",
                          font=('Segoe UI', 10))
    desc_label.pack(pady=(0, 30))
    
    # 按钮演示区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    # 配置网格
    for i in range(3):
        button_frame.grid_columnconfigure(i, weight=1, minsize=200)
    
    # 创建演示按钮
    buttons = [
        ("Plain", "Plain.TButton", "白色背景，深色文字"),
        ("Primary", "Primary.TButton", "蓝色背景，白色文字"),
        ("Success", "Success.TButton", "绿色背景，白色文字"),
        ("Info", "Info.TButton", "灰色背景，白色文字"),
        ("Warning", "Warning.TButton", "橙色背景，白色文字"),
        ("Danger", "Danger.TButton", "红色背景，白色文字")
    ]
    
    for i, (name, style_name, desc) in enumerate(buttons):
        row = i // 3
        col = i % 3
        
        # 按钮容器
        btn_container = ttk.Frame(button_frame)
        btn_container.grid(row=row*2, column=col, padx=10, pady=10, sticky="ew")
        
        # 按钮
        btn = ttk.Button(btn_container, text=name, style=style_name,
                        command=lambda n=name: print(f"点击了 {n} 按钮"))
        btn.pack(fill=tk.X, pady=(0, 5))
        
        # 描述
        desc_lbl = ttk.Label(btn_container, text=desc, 
                           font=('Segoe UI', 8), foreground='#666666')
        desc_lbl.pack()
    
    # 应用示例
    example_frame = ttk.LabelFrame(main_frame, text="🎮 实际应用示例", padding=20)
    example_frame.pack(fill=tk.X, pady=(30, 0))
    
    example_frame.grid_columnconfigure(0, weight=1)
    example_frame.grid_columnconfigure(1, weight=1)
    example_frame.grid_columnconfigure(2, weight=1)
    
    ttk.Button(example_frame, text="➕ 添加动作", style="Primary.TButton").grid(
        row=0, column=0, padx=5, pady=5, sticky="ew")
    ttk.Button(example_frame, text="▶️ 运行工作流", style="Success.TButton").grid(
        row=0, column=1, padx=5, pady=5, sticky="ew")
    ttk.Button(example_frame, text="⏹️ 停止工作流", style="Danger.TButton").grid(
        row=0, column=2, padx=5, pady=5, sticky="ew")
    
    ttk.Button(example_frame, text="🗑️ 清空脚本", style="Warning.TButton").grid(
        row=1, column=0, padx=5, pady=5, sticky="ew")
    ttk.Button(example_frame, text="💾 保存工作流", style="Info.TButton").grid(
        row=1, column=1, padx=5, pady=5, sticky="ew")
    ttk.Button(example_frame, text="📂 加载工作流", style="Plain.TButton").grid(
        row=1, column=2, padx=5, pady=5, sticky="ew")
    
    print("🎨 按钮样式演示程序已启动")
    print("✨ 新的按钮样式特点：")
    print("   - 6种不同的颜色主题")
    print("   - 统一的视觉规范")
    print("   - 优化的hover和pressed状态")
    print("   - 改善的禁用状态对比度")
    
    root.mainloop()

if __name__ == "__main__":
    create_button_demo()
