import cv2
import numpy as np
import mss
import time
import os
from typing import List, Tuple, Optional, Union
import pyautogui
import math

class ImageColorScript:
    """
    完整的图色脚本功能类
    包含区域找图、屏幕找图、区域找色、多点找色、截图、区域截图等功能
    """
    
    def __init__(self):
        """初始化图色脚本"""
        self.screen_width, self.screen_height = pyautogui.size()
        # 禁用 pyautogui 的安全检查
        pyautogui.FAILSAFE = False
        
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        截取屏幕图像
        
        Args:
            region: 截图区域 (x, y, width, height)，None 表示全屏
            
        Returns:
            截取的图像数组 (BGR 格式)
        """
        with mss.mss() as sct:
            if region is None:
                # 全屏截图
                monitor = sct.monitors[1]
                img = sct.grab(monitor)
            else:
                # 区域截图
                x, y, width, height = region
                monitor = {"top": y, "left": x, "width": width, "height": height}
                img = sct.grab(monitor)
            
            img_np = np.array(img)
            img_bgr = cv2.cvtColor(img_np, cv2.COLOR_BGRA2BGR)
            return img_bgr
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None) -> bool:
        """
        保存截图到文件
        
        Args:
            filename: 保存的文件名
            region: 截图区域 (x, y, width, height)，None 表示全屏
            
        Returns:
            是否保存成功
        """
        try:
            img = self.capture_screen(region)
            cv2.imwrite(filename, img)
            return True
        except Exception as e:
            print(f"保存截图失败：{e}")
            return False
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        获取指定坐标的颜色值
        
        Args:
            x: X 坐标
            y: Y 坐标
            
        Returns:
            颜色值 (B, G, R)
        """
        img = self.capture_screen(region=(x, y, 1, 1))
        return tuple(img[0, 0])
    
    def color_distance(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """
        计算两个颜色之间的距离
        
        Args:
            color1: 颜色 1 (B, G, R)
            color2: 颜色 2 (B, G, R)
            
        Returns:
            颜色距离
        """
        return math.sqrt(sum((a - b) ** 2 for a, b in zip(color1, color2)))
    
    def find_image(self, template_path: str, region: Optional[Tuple[int, int, int, int]] = None, 
                   threshold: float = 0.8, max_results: int = 1) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找图像
        
        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量
            
        Returns:
            找到的位置列表 [(x, y, width, height), ...]
        """
        # 读取模板图像
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            raise FileNotFoundError(f"模板文件 {template_path} 不存在")
        
        # 截取搜索区域
        screen_img = self.capture_screen(region)
        
        # 执行模板匹配
        result = cv2.matchTemplate(screen_img, template, cv2.TM_CCOEFF_NORMED)
        
        # 查找匹配位置
        locations = np.where(result >= threshold)
        h, w = template.shape[:2]
        
        # 获取所有匹配位置
        matches = []
        for pt in zip(*locations[::-1]):
            x, y = pt
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((x, y, w, h))
        
        # 非极大值抑制去重
        if len(matches) > 0:
            matches = self._non_max_suppression(matches)
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_image_center(self, template_path: str, region: Optional[Tuple[int, int, int, int]] = None, 
                         threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        查找图像并返回中心坐标
        
        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            
        Returns:
            图像中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_image(template_path, region, threshold, max_results=1)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None
    
    def _non_max_suppression(self, boxes: List[Tuple[int, int, int, int]], 
                           overlap_threshold: float = 0.3) -> List[Tuple[int, int, int, int]]:
        """
        非极大值抑制去重
        
        Args:
            boxes: 边界框列表 [(x, y, w, h), ...]
            overlap_threshold: 重叠阈值
            
        Returns:
            去重后的边界框列表
        """
        if len(boxes) == 0:
            return []
        
        boxes = np.array(boxes)
        pick = []
        
        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 0] + boxes[:, 2]
        y2 = boxes[:, 1] + boxes[:, 3]
        
        area = (x2 - x1 + 1) * (y2 - y1 + 1)
        idxs = np.argsort(y2)
        
        while len(idxs) > 0:
            last = len(idxs) - 1
            i = idxs[last]
            pick.append(i)
            
            xx1 = np.maximum(x1[i], x1[idxs[:last]])
            yy1 = np.maximum(y1[i], y1[idxs[:last]])
            xx2 = np.minimum(x2[i], x2[idxs[:last]])
            yy2 = np.minimum(y2[i], y2[idxs[:last]])
            
            w = np.maximum(0, xx2 - xx1 + 1)
            h = np.maximum(0, yy2 - yy1 + 1)
            
            overlap = (w * h) / area[idxs[:last]]
            
            idxs = np.delete(idxs, np.concatenate(([last], np.where(overlap > overlap_threshold)[0])))
        
        return [tuple(boxes[i]) for i in pick]
    
    def find_color(self, target_color: Tuple[int, int, int], region: Optional[Tuple[int, int, int, int]] = None,
                   tolerance: int = 10, max_results: int = 1) -> List[Tuple[int, int]]:
        """
        在屏幕或指定区域查找颜色
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差
            max_results: 最大返回结果数量
            
        Returns:
            找到的坐标列表 [(x, y), ...]
        """
        # 截取搜索区域
        img = self.capture_screen(region)
        
        # 创建颜色掩码
        lower_bound = np.array([max(0, c - tolerance) for c in target_color])
        upper_bound = np.array([min(255, c + tolerance) for c in target_color])
        
        mask = cv2.inRange(img, lower_bound, upper_bound)
        
        # 查找匹配的像素点
        y_coords, x_coords = np.where(mask == 255)
        
        # 转换为坐标列表
        matches = []
        for x, y in zip(x_coords, y_coords):
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((int(x), int(y)))
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_color_center(self, target_color: Tuple[int, int, int], 
                         region: Optional[Tuple[int, int, int, int]] = None,
                         tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        查找颜色并返回第一个匹配点的坐标
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差
            
        Returns:
            第一个匹配点的坐标 (x, y)，未找到返回 None
        """
        results = self.find_color(target_color, region, tolerance, max_results=1)
        return results[0] if results else None

    def find_multi_color(self, color_points: List[Tuple[int, int, Tuple[int, int, int]]],
                        region: Optional[Tuple[int, int, int, int]] = None,
                        tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        多点找色

        Args:
            color_points: 颜色点列表 [(相对 x, 相对 y, (B, G, R)), ...]
                         第一个点为基准点，其他点为相对于基准点的偏移
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差

        Returns:
            基准点的坐标 (x, y)，未找到返回 None
        """
        if not color_points:
            return None

        # 第一个点作为基准点
        base_offset_x, base_offset_y, base_color = color_points[0]

        # 查找基准点的所有可能位置
        base_matches = self.find_color(base_color, region, tolerance, max_results=1000)

        # 检查每个基准点位置
        for base_x, base_y in base_matches:
            all_match = True

            # 检查其他所有点
            for offset_x, offset_y, target_color in color_points[1:]:
                check_x = base_x + offset_x - base_offset_x
                check_y = base_y + offset_y - base_offset_y

                # 检查坐标是否在有效范围内
                if (check_x < 0 or check_y < 0 or
                    check_x >= self.screen_width or check_y >= self.screen_height):
                    all_match = False
                    break

                # 获取该位置的颜色
                actual_color = self.get_pixel_color(check_x, check_y)

                # 检查颜色是否匹配
                if self.color_distance(actual_color, target_color) > tolerance * math.sqrt(3):
                    all_match = False
                    break

            if all_match:
                return (base_x, base_y)

        return None

    # ==================== 鼠标键盘操作 ====================

    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, interval: float = 0.1):
        """
        鼠标点击

        Args:
            x: X 坐标
            y: Y 坐标
            button: 鼠标按键 ('left', 'right', 'middle')
            clicks: 点击次数
            interval: 点击间隔
        """
        pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)

    def double_click(self, x: int, y: int):
        """双击"""
        pyautogui.doubleClick(x, y)

    def right_click(self, x: int, y: int):
        """右键点击"""
        pyautogui.rightClick(x, y)

    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 1.0):
        """
        拖拽操作

        Args:
            start_x: 起始 X 坐标
            start_y: 起始 Y 坐标
            end_x: 结束 X 坐标
            end_y: 结束 Y 坐标
            duration: 拖拽持续时间
        """
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)

    def scroll(self, x: int, y: int, clicks: int):
        """
        滚轮操作

        Args:
            x: X 坐标
            y: Y 坐标
            clicks: 滚动次数，正数向上，负数向下
        """
        pyautogui.scroll(clicks, x=x, y=y)

    def type_text(self, text: str, interval: float = 0.1):
        """
        输入文本

        Args:
            text: 要输入的文本
            interval: 字符间隔
        """
        pyautogui.typewrite(text, interval=interval)

    def press_key(self, key: str):
        """
        按键

        Args:
            key: 按键名称 (如 'enter', 'space', 'ctrl', 'alt' 等)
        """
        pyautogui.press(key)

    def key_combination(self, *keys):
        """
        组合键

        Args:
            keys: 按键组合 (如 'ctrl', 'c')
        """
        pyautogui.hotkey(*keys)

    # ==================== 图像处理和比较 ====================

    def compare_images(self, img1_path: str, img2_path: str, method: str = 'histogram') -> float:
        """
        比较两张图片的相似度

        Args:
            img1_path: 图片 1 路径
            img2_path: 图片 2 路径
            method: 比较方法 ('histogram', 'mse', 'ssim', 'template')

        Returns:
            相似度 (0-1)，1 表示完全相同
        """
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)

        if img1 is None or img2 is None:
            return 0.0

        if method == 'histogram':
            return self._compare_histogram(img1, img2)
        elif method == 'mse':
            return self._compare_mse(img1, img2)
        elif method == 'ssim':
            return self._compare_ssim(img1, img2)
        elif method == 'template':
            return self._compare_template(img1, img2)
        else:
            return self._compare_histogram(img1, img2)

    def _compare_histogram(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用直方图比较图像相似度"""
        # 转换为 HSV 颜色空间
        hsv1 = cv2.cvtColor(img1, cv2.COLOR_BGR2HSV)
        hsv2 = cv2.cvtColor(img2, cv2.COLOR_BGR2HSV)

        # 计算直方图
        hist1 = cv2.calcHist([hsv1], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])
        hist2 = cv2.calcHist([hsv2], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])

        # 使用相关性比较
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        return max(0.0, correlation)  # 确保返回值在 0-1 之间

    def _compare_mse(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用均方误差比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 计算均方误差
        mse = np.mean((img1.astype(float) - img2.astype(float)) ** 2)

        # 转换为相似度 (MSE 越小，相似度越高)
        max_mse = 255.0 ** 2  # 最大可能的 MSE
        similarity = 1.0 - (mse / max_mse)
        return max(0.0, similarity)

    def _compare_ssim(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用结构相似性指数比较图像"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算 SSIM (简化版本)
        mu1 = cv2.GaussianBlur(gray1.astype(float), (11, 11), 1.5)
        mu2 = cv2.GaussianBlur(gray2.astype(float), (11, 11), 1.5)

        mu1_sq = mu1 * mu1
        mu2_sq = mu2 * mu2
        mu1_mu2 = mu1 * mu2

        sigma1_sq = cv2.GaussianBlur(gray1.astype(float) * gray1.astype(float), (11, 11), 1.5) - mu1_sq
        sigma2_sq = cv2.GaussianBlur(gray2.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu2_sq
        sigma12 = cv2.GaussianBlur(gray1.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu1_mu2

        c1 = (0.01 * 255) ** 2
        c2 = (0.03 * 255) ** 2

        ssim_map = ((2 * mu1_mu2 + c1) * (2 * sigma12 + c2)) / ((mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2))
        return float(np.mean(ssim_map))

    def _compare_template(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用模板匹配比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 使用模板匹配
        result = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)
        return max(0.0, float(np.max(result)))  # 确保返回值非负

    def find_image_in_image(self, large_img_path: str, small_img_path: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        在大图中查找小图的位置（专门用于区域图在全屏图中的定位）

        Args:
            large_img_path: 大图路径
            small_img_path: 小图路径
            threshold: 相似度阈值

        Returns:
            小图在大图中的位置 (x, y)，未找到返回 None
        """
        large_img = cv2.imread(large_img_path)
        small_img = cv2.imread(small_img_path)

        if large_img is None or small_img is None:
            return None

        # 转换为灰度图
        large_gray = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
        small_gray = cv2.cvtColor(small_img, cv2.COLOR_BGR2GRAY)

        # 模板匹配
        result = cv2.matchTemplate(large_gray, small_gray, cv2.TM_CCOEFF_NORMED)

        # 查找最佳匹配位置
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val >= threshold:
            return max_loc
        else:
            return None

    def wait_for_image(self, template_path: str, timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      threshold: float = 0.8, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待图像出现

        Args:
            template_path: 模板图像路径
            timeout: 超时时间（秒）
            region: 搜索区域
            threshold: 相似度阈值
            check_interval: 检查间隔

        Returns:
            图像中心坐标，超时返回 None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_image_center(template_path, region, threshold)
            if result:
                return result
            time.sleep(check_interval)

        return None

    def wait_for_color(self, target_color: Tuple[int, int, int], timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      tolerance: int = 10, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待颜色出现

        Args:
            target_color: 目标颜色
            timeout: 超时时间（秒）
            region: 搜索区域
            tolerance: 颜色容差
            check_interval: 检查间隔

        Returns:
            颜色坐标，超时返回 None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_color_center(target_color, region, tolerance)
            if result:
                return result
            time.sleep(check_interval)

        return None


# ==================== 使用示例和测试 ====================

def demo_basic_functions():
    """基础功能演示"""
    print("=== 图色脚本功能演示 ===")

    # 创建脚本实例
    script = ImageColorScript()

    print("1. 截图功能演示")
    # 全屏截图
    if script.save_screenshot("full_screen.png"):
        print("   ✓ 全屏截图保存成功：full_screen.png")

    # 区域截图 (左上角 500x300 区域)
    if script.save_screenshot("region_screen.png", region=(0, 0, 500, 300)):
        print("   ✓ 区域截图保存成功：region_screen.png")

    print("\n2. 颜色获取演示")
    # 获取屏幕中心点颜色
    center_x, center_y = script.screen_width // 2, script.screen_height // 2
    color = script.get_pixel_color(center_x, center_y)
    print(f"   屏幕中心点 ({center_x}, {center_y}) 的颜色：{color}")

    print("\n3. 找色功能演示")
    # 查找白色 (255, 255, 255)
    white_positions = script.find_color((255, 255, 255), max_results=5)
    print(f"   找到 {len(white_positions)} 个白色像素点")
    if white_positions:
        print(f"   前几个位置：{white_positions[:3]}")


def demo_advanced_functions():
    """高级功能演示"""
    print("\n=== 高级功能演示 ===")

    script = ImageColorScript()

    print("1. 多点找色演示")
    # 定义一个简单的颜色模式 (例如：查找一个小的白色方块)
    color_pattern = [
        (0, 0, (255, 255, 255)),    # 基准点：白色
        (1, 0, (255, 255, 255)),    # 右边一个像素：白色
        (0, 1, (255, 255, 255)),    # 下边一个像素：白色
        (1, 1, (255, 255, 255)),    # 右下角：白色
    ]

    result = script.find_multi_color(color_pattern, tolerance=10)
    if result:
        print(f"   ✓ 找到颜色模式，位置：{result}")
    else:
        print("   ✗ 未找到指定的颜色模式")

    print("\n2. 等待功能演示")
    print("   注意：等待功能需要实际的图像文件，这里仅演示调用方式")

    print("\n3. 鼠标键盘操作演示")
    print("   注意：以下操作会实际控制鼠标键盘，请小心使用")

    # 获取当前鼠标位置
    current_pos = pyautogui.position()
    print(f"   当前鼠标位置：{current_pos}")


def test_color_functions():
    """测试颜色相关功能"""
    print("\n=== 颜色功能测试 ===")

    script = ImageColorScript()

    # 测试颜色距离计算
    color1 = (255, 0, 0)    # 红色
    color2 = (0, 255, 0)    # 绿色
    color3 = (255, 10, 10)  # 接近红色

    distance1 = script.color_distance(color1, color2)
    distance2 = script.color_distance(color1, color3)

    print(f"红色与绿色的距离：{distance1:.2f}")
    print(f"红色与接近红色的距离：{distance2:.2f}")

    # 测试在小区域内查找颜色
    region = (0, 0, 100, 100)  # 左上角 100x100 区域
    colors_found = script.find_color((255, 255, 255), region=region, max_results=10)
    print(f"在区域 {region} 内找到 {len(colors_found)} 个白色像素")


def main():
    """主函数 - 运行所有演示"""
    try:
        demo_basic_functions()
        demo_advanced_functions()
        test_color_functions()

        print("\n=== 演示完成 ===")
        print("所有功能演示已完成！")
        print("\n可用的主要功能：")
        print("• capture_screen() - 截图")
        print("• save_screenshot() - 保存截图")
        print("• find_image() - 找图")
        print("• find_color() - 找色")
        print("• find_multi_color() - 多点找色")
        print("• get_pixel_color() - 获取像素颜色")
        print("• click() - 鼠标点击")
        print("• type_text() - 输入文本")
        print("• wait_for_image() - 等待图像")
        print("• compare_images() - 图像比较")
        print("• find_image_in_image() - 在大图中查找小图")

    except Exception as e:
        print(f"演示过程中出现错误：{e}")
        print("请确保已安装所需的依赖库：")
        print("pip install opencv-python numpy mss pyautogui")


if __name__ == "__main__":
    main()
