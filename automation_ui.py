import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
try:
    from PIL import Image, ImageTk, ImageGrab
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available - some features will be disabled")

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("OpenCV not available - some features will be disabled")

import ctypes
import platform
import json
import os

try:
    from pynput import keyboard
    from pynput.keyboard import Key, Listener
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("pynput not available - hotkeys will be disabled")

try:
    from LogiUsbimplementation import ImplementationA
    LOGI_AVAILABLE = True
except ImportError:
    LOGI_AVAILABLE = False
    print("LogiUsbimplementation not available - automation will be disabled")
# Removed placeholder ImplementationA

class AutomationApp:
    # Removed DIAGNOSTIC PLACEHOLDER for add_action_to_script

    def __init__(self, root):
        self.root = root
        self.root.title("Automation UI")
        # Initial size
        window_width = 1200
        window_height = 900
        self.root.geometry(f"{window_width}x{window_height}")

        # Center the window
        self.root.update_idletasks() # Update tasks to get correct window info
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x_coordinate = (screen_width // 2) - (window_width // 2)
        y_coordinate = (screen_height // 2) - (window_height // 2)
        self.root.geometry(f"{window_width}x{window_height}+{x_coordinate}+{y_coordinate}")

        self.template_image = None
        self.automation_thread = None
        self.is_automating = False
        self.mouse_controller = None
        self.wyUsbHandle = None # To store the handle from wyhz_init()

        # Scripting variables
        self.script_steps = []
        self.is_workflow_running = False
        self.workflow_thread = None

        # Hotkey settings
        self.hotkey_start = "F10"
        self.hotkey_stop = "F12"
        self.hotkey_settings_file = "hotkey_settings.json"
        self.global_listener = None  # Global keyboard listener

        # Modern Styling
        self.setup_modern_styles()

        # --- Modern Main Layout ---
        self.setup_main_layout()

        # Content will be created by setup_content_areas()






        # Modern Status Bar
        self.create_status_bar()

        # Autosave setup
        self.autosave_filepath = "autosave_workflow.json"
        self._autosave_load()
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # Setup hotkeys and menu at the end of initialization
        self._initialize_hotkeys_and_menu()

    def setup_modern_styles(self):
        """Setup modern styling for the application"""
        style = ttk.Style()

        # Use a more modern theme
        available_themes = style.theme_names()
        if 'vista' in available_themes:
            style.theme_use('vista')
        elif 'winnative' in available_themes:
            style.theme_use('winnative')
        else:
            style.theme_use('clam')

        # Modern color scheme
        self.colors = {
            'primary': '#2196F3',      # Blue
            'primary_dark': '#1976D2',
            'secondary': '#FF9800',    # Orange
            'success': '#4CAF50',      # Green
            'danger': '#F44336',       # Red
            'warning': '#FF9800',      # Orange
            'info': '#2196F3',         # Blue
            'light': '#F5F5F5',        # Light gray
            'dark': '#212121',         # Dark gray
            'white': '#FFFFFF',
            'border': '#E0E0E0',
            'text': '#333333',
            'text_light': '#666666'
        }

        # Configure modern button styles with enhanced appearance and better contrast
        style.configure("Modern.TButton",
                       padding=(12, 8),
                       relief="flat",
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9))

        style.map("Modern.TButton",
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', self.colors['primary_dark']),
                           ('disabled', '#E0E0E0')],  # Lighter disabled background
                 foreground=[('disabled', '#424242')],  # Darker disabled text for better contrast
                 bordercolor=[('disabled', '#BDBDBD')])  # Subtle border for disabled state

        # Success button style with enhanced hover effects and better contrast
        style.configure("Success.TButton",
                       padding=(12, 8),
                       relief="flat",
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9, 'bold'))

        style.map("Success.TButton",
                 background=[('active', '#45A049'),
                           ('pressed', '#45A049'),
                           ('disabled', '#E0E0E0')],  # Consistent disabled background
                 foreground=[('disabled', '#424242')],  # Better contrast for disabled text
                 bordercolor=[('disabled', '#BDBDBD')])

        # Danger button style with enhanced hover effects and better contrast
        style.configure("Danger.TButton",
                       padding=(12, 8),
                       relief="flat",
                       background=self.colors['danger'],
                       foreground='white',
                       borderwidth=1,
                       focuscolor='none',
                       font=('Segoe UI', 9, 'bold'))

        style.map("Danger.TButton",
                 background=[('active', '#D32F2F'),
                           ('pressed', '#D32F2F'),
                           ('disabled', '#E0E0E0')],  # Consistent disabled background
                 foreground=[('disabled', '#424242')],  # Better contrast for disabled text
                 bordercolor=[('disabled', '#BDBDBD')])

        # Modern entry style with better focus indication
        style.configure("Modern.TEntry",
                       padding=(10, 8),
                       relief="solid",
                       borderwidth=1,
                       focuscolor=self.colors['primary'],
                       font=('Segoe UI', 9),
                       fieldbackground='white')

        style.map("Modern.TEntry",
                 bordercolor=[('focus', self.colors['primary']),
                            ('!focus', self.colors['border'])],
                 lightcolor=[('focus', self.colors['primary'])])

        # Modern label style with better typography
        style.configure("Modern.TLabel",
                       padding=(5, 3),
                       foreground=self.colors['text'],
                       font=('Segoe UI', 9))

        # Card frame style with subtle shadow effect
        style.configure("Card.TFrame",
                       relief="flat",
                       borderwidth=1,
                       background=self.colors['white'])

        # Header label style with better hierarchy
        style.configure("Header.TLabel",
                       padding=(8, 5),
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['text'])

        # Title label style for main sections
        style.configure("Title.TLabel",
                       padding=(10, 8),
                       font=('Segoe UI', 14, 'bold'),
                       foreground=self.colors['primary'])

        # Combobox style
        style.configure("Modern.TCombobox",
                       padding=(8, 6),
                       relief="solid",
                       borderwidth=1,
                       font=('Segoe UI', 9),
                       fieldbackground='white')

        style.map("Modern.TCombobox",
                 bordercolor=[('focus', self.colors['primary']),
                            ('!focus', self.colors['border'])],
                 selectbackground=[('focus', self.colors['primary'])])

        # LabelFrame style for cards
        style.configure("Card.TLabelframe",
                       relief="flat",
                       borderwidth=1,
                       background=self.colors['white'],
                       labelmargins=(10, 5, 10, 5))

        style.configure("Card.TLabelframe.Label",
                       font=('Segoe UI', 11, 'bold'),
                       foreground=self.colors['primary'],
                       background=self.colors['white'])

    def setup_main_layout(self):
        """Setup the main layout with modern design"""
        # Main container with padding
        self.main_container = ttk.Frame(self.root, style="Card.TFrame")
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create main content area with scrolling
        self.setup_scrollable_content()

        # Setup the content areas
        self.setup_content_areas()

    def setup_scrollable_content(self):
        """Setup scrollable content area"""
        # Canvas and scrollbar for scrolling
        self.main_canvas = tk.Canvas(self.main_container,
                                   highlightthickness=0,
                                   background=self.colors['light'])
        self.main_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(self.main_container,
                                orient=tk.VERTICAL,
                                command=self.main_canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.main_canvas.configure(yscrollcommand=scrollbar.set)

        # Scrollable frame
        self.scrollable_frame = ttk.Frame(self.main_canvas)
        self.canvas_frame_id = self.main_canvas.create_window(
            (0, 0), window=self.scrollable_frame, anchor="nw")

        # Bind events
        self.scrollable_frame.bind("<Configure>", self._on_frame_configure)
        self.main_canvas.bind("<Configure>", self._on_canvas_configure)
        self.root.bind_all("<MouseWheel>", self._on_mouse_wheel)

    def setup_content_areas(self):
        """Setup the main content areas with modern layout"""
        # Create a grid layout for better organization
        self.scrollable_frame.grid_columnconfigure(0, weight=1)
        self.scrollable_frame.grid_columnconfigure(1, weight=1)

        # Row 0: Device Configuration (spans both columns)
        self.create_device_config_card()

        # Row 1: Template Management (left) and Action Config (right)
        self.create_template_card()
        self.create_action_config_card()

        # Row 2: Automation Control (spans both columns)
        self.create_automation_control_card()

        # Row 3: Script Workflow (spans both columns)
        self.create_workflow_card()

    def create_device_config_card(self):
        """Create device configuration card"""
        card_frame = ttk.LabelFrame(self.scrollable_frame, text="🔧 设备配置",
                                   style="Card.TLabelframe", padding=20)
        card_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=15, pady=10)

        # Configure grid
        card_frame.grid_columnconfigure(1, weight=1)
        card_frame.grid_columnconfigure(3, weight=1)

        # VID input
        ttk.Label(card_frame, text="VID:", style="Modern.TLabel").grid(
            row=0, column=0, padx=(0, 10), pady=5, sticky="w")
        self.vid_entry = ttk.Entry(card_frame, width=15, style="Modern.TEntry")
        self.vid_entry.grid(row=0, column=1, padx=(0, 20), pady=5, sticky="w")
        self.vid_entry.insert(0, "048D")

        # PID input
        ttk.Label(card_frame, text="PID:", style="Modern.TLabel").grid(
            row=0, column=2, padx=(0, 10), pady=5, sticky="w")
        self.pid_entry = ttk.Entry(card_frame, width=15, style="Modern.TEntry")
        self.pid_entry.grid(row=0, column=3, pady=5, sticky="w")
        self.pid_entry.insert(0, "7632")

    def create_template_card(self):
        """Create template management card"""
        card_frame = ttk.LabelFrame(self.scrollable_frame, text="🖼️ 模板管理",
                                   style="Card.TLabelframe", padding=20)
        card_frame.grid(row=1, column=0, sticky="nsew", padx=(15, 8), pady=10)

        # Configure grid
        card_frame.grid_columnconfigure(0, weight=1)
        card_frame.grid_rowconfigure(1, weight=1)

        # Template display area
        display_frame = ttk.Frame(card_frame)
        display_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))

        self.template_display = ttk.Label(display_frame, text="暂无模板图像",
                                        style="Modern.TLabel",
                                        relief="solid", borderwidth=1)
        self.template_display.pack(pady=10, fill="x")

        # Buttons frame
        button_frame = ttk.Frame(card_frame)
        button_frame.grid(row=1, column=0, sticky="ew")
        button_frame.grid_columnconfigure(0, weight=1)

        self.capture_template_button = ttk.Button(button_frame,
                                                text="📷 捕获模板",
                                                command=self.capture_template,
                                                style="Modern.TButton")
        self.capture_template_button.grid(row=0, column=0, sticky="ew", pady=2)

        self.load_template_button = ttk.Button(button_frame,
                                             text="📁 加载文件",
                                             command=self.load_template_from_file,
                                             style="Modern.TButton")
        self.load_template_button.grid(row=1, column=0, sticky="ew", pady=2)

        # Status label
        self.template_label = ttk.Label(button_frame, text="未捕获模板",
                                      style="Modern.TLabel", wraplength=200)
        self.template_label.grid(row=2, column=0, pady=(10, 0))

    def create_action_config_card(self):
        """Create action configuration card"""
        card_frame = ttk.LabelFrame(self.scrollable_frame, text="⚙️ 动作配置",
                                   style="Card.TLabelframe", padding=20)
        card_frame.grid(row=1, column=1, sticky="nsew", padx=(8, 15), pady=10)

        # Configure grid
        for i in range(6):
            card_frame.grid_columnconfigure(i, weight=1)

        # Found location (read-only)
        ttk.Label(card_frame, text="发现位置：", style="Header.TLabel").grid(
            row=0, column=0, columnspan=6, sticky="w", pady=(0, 10))

        ttk.Label(card_frame, text="X:", style="Modern.TLabel").grid(
            row=1, column=0, padx=2, pady=2, sticky="w")
        self.found_x_entry = ttk.Entry(card_frame, width=8, state="readonly", style="Modern.TEntry")
        self.found_x_entry.grid(row=1, column=1, padx=2, pady=2, sticky="ew")

        ttk.Label(card_frame, text="Y:", style="Modern.TLabel").grid(
            row=1, column=2, padx=2, pady=2, sticky="w")
        self.found_y_entry = ttk.Entry(card_frame, width=8, state="readonly", style="Modern.TEntry")
        self.found_y_entry.grid(row=1, column=3, padx=2, pady=2, sticky="ew")

        ttk.Label(card_frame, text="置信度：", style="Modern.TLabel").grid(
            row=1, column=4, padx=2, pady=2, sticky="w")
        self.confidence_entry = ttk.Entry(card_frame, width=8, state="readonly", style="Modern.TEntry")
        self.confidence_entry.grid(row=1, column=5, padx=2, pady=2, sticky="ew")

        # Offset configuration
        ttk.Label(card_frame, text="偏移设置：", style="Header.TLabel").grid(
            row=2, column=0, columnspan=6, sticky="w", pady=(15, 5))

        ttk.Label(card_frame, text="X 偏移：", style="Modern.TLabel").grid(
            row=3, column=0, padx=2, pady=2, sticky="w")
        self.offset_x_entry = ttk.Entry(card_frame, width=8, style="Modern.TEntry")
        self.offset_x_entry.grid(row=3, column=1, padx=2, pady=2, sticky="ew")
        self.offset_x_entry.insert(0, "0")

        ttk.Label(card_frame, text="Y 偏移：", style="Modern.TLabel").grid(
            row=3, column=2, padx=2, pady=2, sticky="w")
        self.offset_y_entry = ttk.Entry(card_frame, width=8, style="Modern.TEntry")
        self.offset_y_entry.grid(row=3, column=3, padx=2, pady=2, sticky="ew")
        self.offset_y_entry.insert(0, "0")

        # Action type
        ttk.Label(card_frame, text="动作类型：", style="Modern.TLabel").grid(
            row=4, column=0, padx=2, pady=(10, 2), sticky="w")
        self.action_type_combo = ttk.Combobox(card_frame,
                                            values=["Left Click", "Right Click", "Double Click", "Press Key"],
                                            width=12, state="readonly", style="Modern.TCombobox")
        self.action_type_combo.grid(row=4, column=1, columnspan=2, padx=2, pady=(10, 2), sticky="ew")
        self.action_type_combo.current(0)
        self.action_type_combo.bind("<<ComboboxSelected>>", self.on_action_type_change)

        # Key to press
        ttk.Label(card_frame, text="按键：", style="Modern.TLabel").grid(
            row=4, column=3, padx=2, pady=(10, 2), sticky="w")
        self.key_press_entry = ttk.Entry(card_frame, width=10, state="disabled", style="Modern.TEntry")
        self.key_press_entry.grid(row=4, column=4, columnspan=2, padx=2, pady=(10, 2), sticky="ew")

        # Step interval
        ttk.Label(card_frame, text="步骤等待 (秒):", style="Modern.TLabel").grid(
            row=5, column=0, padx=2, pady=(10, 2), sticky="w")
        self.step_interval_entry = ttk.Entry(card_frame, width=8, style="Modern.TEntry")
        self.step_interval_entry.grid(row=5, column=1, padx=2, pady=(10, 2), sticky="ew")
        self.step_interval_entry.insert(0, "1.0")

    def create_automation_control_card(self):
        """Create automation control card"""
        card_frame = ttk.LabelFrame(self.scrollable_frame, text="🎮 自动化控制",
                                   style="Card.TLabelframe", padding=20)
        card_frame.grid(row=2, column=0, columnspan=2, sticky="ew", padx=15, pady=10)

        # Configure grid
        card_frame.grid_columnconfigure(1, weight=1)

        # Interval setting
        ttk.Label(card_frame, text="执行间隔 (秒):", style="Modern.TLabel").grid(
            row=0, column=0, padx=(0, 10), pady=5, sticky="w")
        self.interval_entry = ttk.Entry(card_frame, width=10, style="Modern.TEntry")
        self.interval_entry.grid(row=0, column=1, padx=(0, 20), pady=5, sticky="w")
        self.interval_entry.insert(0, "5")

        # Control button
        self.start_stop_button = ttk.Button(card_frame, text="▶️ 开始自动化",
                                          command=self.toggle_automation,
                                          style="Success.TButton")
        self.start_stop_button.grid(row=0, column=2, padx=10, pady=5, sticky="e")

    def create_workflow_card(self):
        """Create workflow management card"""
        card_frame = ttk.LabelFrame(self.scrollable_frame, text="📋 脚本工作流",
                                   style="Card.TLabelframe", padding=20)
        card_frame.grid(row=3, column=0, columnspan=2, sticky="nsew", padx=15, pady=10)

        # Configure grid
        card_frame.grid_columnconfigure(0, weight=1)
        card_frame.grid_rowconfigure(1, weight=1)

        # Control panel with responsive layout
        control_frame = ttk.Frame(card_frame)
        control_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        control_frame.grid_columnconfigure(0, weight=1)

        # Settings section with improved layout
        settings_frame = ttk.LabelFrame(control_frame, text="⚙️ 工作流设置", style="Card.TLabelframe", padding=15)
        settings_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        settings_frame.grid_columnconfigure(1, weight=1)
        settings_frame.grid_columnconfigure(3, weight=1)

        # Execution count setting
        ttk.Label(settings_frame, text="执行次数：", style="Modern.TLabel").grid(
            row=0, column=0, padx=(0, 8), pady=5, sticky="w")
        self.workflow_runs_entry = ttk.Entry(settings_frame, width=10, style="Modern.TEntry")
        self.workflow_runs_entry.grid(row=0, column=1, padx=(0, 25), pady=5, sticky="w")
        self.workflow_runs_entry.insert(0, "1")

        # Help text for execution count
        ttk.Label(settings_frame, text="(0=无限循环)", style="Modern.TLabel",
                 foreground=self.colors['text_light']).grid(
            row=1, column=0, columnspan=2, padx=(0, 25), pady=(0, 5), sticky="w")

        # Step interval setting
        ttk.Label(settings_frame, text="步骤间隔：", style="Modern.TLabel").grid(
            row=0, column=2, padx=(0, 8), pady=5, sticky="w")
        self.workflow_interval_entry = ttk.Entry(settings_frame, width=10, style="Modern.TEntry")
        self.workflow_interval_entry.grid(row=0, column=3, pady=5, sticky="w")
        self.workflow_interval_entry.insert(0, "0.5")

        # Help text for interval
        ttk.Label(settings_frame, text="(秒)", style="Modern.TLabel",
                 foreground=self.colors['text_light']).grid(
            row=1, column=2, columnspan=2, pady=(0, 5), sticky="w")

        # Action buttons section with improved organization
        actions_frame = ttk.LabelFrame(control_frame, text="🎮 工作流操作", style="Card.TLabelframe", padding=15)
        actions_frame.grid(row=1, column=0, sticky="ew", pady=5)

        # Configure responsive grid - 3 columns for better layout on smaller screens
        for i in range(3):
            actions_frame.grid_columnconfigure(i, weight=1, minsize=140)

        # Row 1: Primary actions (Add, Run, Stop)
        self.add_action_button = ttk.Button(actions_frame, text="➕ 添加动作",
                                          command=self.add_action_to_script,
                                          style="Modern.TButton")
        self.add_action_button.grid(row=0, column=0, sticky="ew", padx=3, pady=3)

        self.run_workflow_button = ttk.Button(actions_frame, text="▶️ 运行工作流",
                                            command=self.run_workflow,
                                            style="Success.TButton")
        self.run_workflow_button.grid(row=0, column=1, sticky="ew", padx=3, pady=3)

        self.stop_workflow_button = ttk.Button(actions_frame, text="⏹️ 停止工作流",
                                             command=self.stop_workflow, state="disabled",
                                             style="Danger.TButton")
        self.stop_workflow_button.grid(row=0, column=2, sticky="ew", padx=3, pady=3)

        # Row 2: Secondary actions (Clear, Save, Load)
        self.clear_script_button = ttk.Button(actions_frame, text="🗑️ 清空脚本",
                                            command=self.clear_script,
                                            style="Modern.TButton")
        self.clear_script_button.grid(row=1, column=0, sticky="ew", padx=3, pady=3)

        self.save_workflow_button = ttk.Button(actions_frame, text="💾 保存工作流",
                                             command=self.save_workflow,
                                             style="Modern.TButton")
        self.save_workflow_button.grid(row=1, column=1, sticky="ew", padx=3, pady=3)

        self.load_workflow_button = ttk.Button(actions_frame, text="📂 加载工作流",
                                             command=self.load_workflow,
                                             style="Modern.TButton")
        self.load_workflow_button.grid(row=1, column=2, sticky="ew", padx=3, pady=3)

        # Script display area with enhanced styling
        script_frame = ttk.LabelFrame(card_frame, text="📝 脚本内容", style="Card.TLabelframe")
        script_frame.grid(row=1, column=0, sticky="nsew", pady=(15, 0))
        script_frame.grid_columnconfigure(0, weight=1)
        script_frame.grid_rowconfigure(0, weight=1)

        # Text area with scrollbar
        text_container = ttk.Frame(script_frame)
        text_container.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        text_container.grid_columnconfigure(0, weight=1)
        text_container.grid_rowconfigure(0, weight=1)

        self.script_text_area = tk.Text(text_container, height=14, width=85,
                                      state="disabled", wrap=tk.WORD,
                                      font=('Consolas', 10),
                                      bg='#FAFAFA',
                                      fg=self.colors['text'],
                                      relief="solid", borderwidth=1,
                                      selectbackground=self.colors['primary'],
                                      selectforeground='white',
                                      insertbackground=self.colors['primary'])
        self.script_text_area.grid(row=0, column=0, sticky="nsew", padx=(0, 5))

        # Scrollbar for text area
        text_scrollbar = ttk.Scrollbar(text_container, orient=tk.VERTICAL,
                                     command=self.script_text_area.yview)
        text_scrollbar.grid(row=0, column=1, sticky="ns")
        self.script_text_area.configure(yscrollcommand=text_scrollbar.set)

        # Add step counter label
        step_info_frame = ttk.Frame(script_frame)
        step_info_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))

        self.step_counter_label = ttk.Label(step_info_frame, text="步骤数量：0",
                                          style="Modern.TLabel")
        self.step_counter_label.pack(side=tk.LEFT)

        # Add clear indication when script is empty
        self.empty_script_label = ttk.Label(text_container,
                                          text="📝 暂无脚本步骤\n\n点击 '➕ 添加动作' 开始创建工作流",
                                          style="Modern.TLabel",
                                          justify=tk.CENTER)
        self.empty_script_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

    def create_status_bar(self):
        """Create modern status bar"""
        status_frame = ttk.Frame(self.root, style="Card.TFrame")
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=15, pady=(0, 15))

        self.status_bar = ttk.Label(status_frame, text="🟢 状态：就绪",
                                  style="Modern.TLabel",
                                  relief="flat", anchor=tk.W)
        self.status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=15, pady=8)

    def _initialize_hotkeys_and_menu(self):
        """Initialize hotkeys and menu after all methods are defined"""
        self._load_hotkey_settings()
        self._setup_global_hotkeys()
        self._create_menu_bar()

    def _create_menu_bar(self):
        """Create the menu bar with settings menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="快捷键设置", command=self.open_hotkey_settings)

    def _load_hotkey_settings(self):
        """Load hotkey settings from file"""
        try:
            if os.path.exists(self.hotkey_settings_file):
                with open(self.hotkey_settings_file, 'r') as f:
                    settings = json.load(f)
                    self.hotkey_start = settings.get("start", "F10")
                    self.hotkey_stop = settings.get("stop", "F12")
        except Exception as e:
            print(f"Error loading hotkey settings: {e}")

    def _save_hotkey_settings(self):
        """Save hotkey settings to file"""
        try:
            settings = {
                "start": self.hotkey_start,
                "stop": self.hotkey_stop
            }
            with open(self.hotkey_settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            print(f"Error saving hotkey settings: {e}")

    def open_hotkey_settings(self):
        """Open hotkey settings dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("快捷键设置")
        dialog.geometry("300x200")
        dialog.resizable(False, False)

        # Center the dialog
        dialog.transient(self.root)
        dialog.grab_set()

        # Start hotkey setting
        ttk.Label(dialog, text="启动工作流快捷键：").pack(pady=10)
        start_var = tk.StringVar(value=self.hotkey_start)
        start_entry = ttk.Entry(dialog, textvariable=start_var, width=20)
        start_entry.pack(pady=5)

        # Stop hotkey setting
        ttk.Label(dialog, text="停止工作流快捷键：").pack(pady=10)
        stop_var = tk.StringVar(value=self.hotkey_stop)
        stop_entry = ttk.Entry(dialog, textvariable=stop_var, width=20)
        stop_entry.pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        def save_settings():
            self.hotkey_start = start_var.get().strip()
            self.hotkey_stop = stop_var.get().strip()
            self._save_hotkey_settings()
            self._setup_global_hotkeys()
            messagebox.showinfo("设置", "快捷键设置已保存")
            dialog.destroy()

        def cancel_settings():
            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel_settings).pack(side=tk.LEFT, padx=5)

    def _setup_global_hotkeys(self):
        """Setup global hotkeys using pynput"""
        if not PYNPUT_AVAILABLE:
            self.update_status("全局快捷键功能不可用 - 缺少 pynput 库", "warning")
            print("pynput not available - global hotkeys disabled")
            return

        try:
            # Stop existing listener if any
            if self.global_listener:
                self.global_listener.stop()

            # Create key mapping
            key_mapping = {
                'F1': Key.f1, 'F2': Key.f2, 'F3': Key.f3, 'F4': Key.f4,
                'F5': Key.f5, 'F6': Key.f6, 'F7': Key.f7, 'F8': Key.f8,
                'F9': Key.f9, 'F10': Key.f10, 'F11': Key.f11, 'F12': Key.f12
            }

            start_key = key_mapping.get(self.hotkey_start.upper())
            stop_key = key_mapping.get(self.hotkey_stop.upper())

            if not start_key or not stop_key:
                self.update_status(f"不支持的快捷键：{self.hotkey_start}/{self.hotkey_stop}")
                return

            def on_press(key):
                try:
                    if key == start_key:
                        # Use root.after to ensure thread safety
                        self.root.after(0, self._hotkey_start_workflow)
                    elif key == stop_key:
                        self.root.after(0, self._hotkey_stop_workflow)
                except Exception as e:
                    print(f"Hotkey error: {e}")

            # Start global listener
            self.global_listener = Listener(on_press=on_press)
            self.global_listener.daemon = True
            self.global_listener.start()

            print(f"Global hotkeys started: {self.hotkey_start}=start, {self.hotkey_stop}=stop")
            self.update_status(f"全局快捷键已设置：{self.hotkey_start}=启动，{self.hotkey_stop}=停止")
        except Exception as e:
            self.update_status(f"全局快捷键设置失败：{e}")
            print(f"Global hotkey setup error: {e}")

    def _hotkey_start_workflow(self):
        """Handle start workflow hotkey"""
        if not self.is_workflow_running and self.script_steps:
            self.run_workflow()

    def _hotkey_stop_workflow(self):
        """Handle stop workflow hotkey"""
        if self.is_workflow_running:
            self.stop_workflow()

    def _on_closing(self):
        self._autosave_save()
        # Stop global hotkey listener
        if self.global_listener:
            self.global_listener.stop()
        self.root.destroy()

    def _autosave_save(self):
        if not self.script_steps:
            # If script is empty, check if an old autosave file exists and remove it
            if os.path.exists(self.autosave_filepath):
                try:
                    asset_dir_name = os.path.splitext(self.autosave_filepath)[0] + "_assets"
                    if os.path.exists(asset_dir_name):
                        import shutil
                        shutil.rmtree(asset_dir_name)
                    os.remove(self.autosave_filepath)
                    print("Autosave file and assets removed as script is empty.")
                except Exception as e:
                    print(f"Error removing old autosave file: {e}")
            return

        # Re-use the save_workflow logic but with a fixed path
        filepath = self.autosave_filepath
        base_dir = os.path.dirname(os.path.abspath(filepath)) # Use absolute path for reliability
        asset_dir_name = os.path.splitext(os.path.basename(filepath))[0] + "_assets"
        asset_path = os.path.join(base_dir, asset_dir_name)

        try:
            if not os.path.exists(asset_path):
                os.makedirs(asset_path)
        except OSError as e:
            print(f"Autosave Error: Failed to create asset directory: {e}")
            return

        workflow_data = {
            "vid": self.vid_entry.get(),
            "pid": self.pid_entry.get(),
            "steps": []
        }

        try:
            for i, step in enumerate(self.script_steps):
                template_filename = f"step_{i+1}_template.png"
                template_filepath = os.path.join(asset_path, template_filename)
                if step["template_image"] is not None:
                    success = cv2.imwrite(template_filepath, step["template_image"])
                    if not success:
                        print(f"Autosave Error: Failed to save template image for step {i+1}")
                        continue
                else:
                    print(f"Autosave Warning: Step {i+1} has no template image to save")
                    continue
                step_data = {
                    "template_path": os.path.join(asset_dir_name, template_filename),
                    "offset_x": step["offset_x"],
                    "offset_y": step["offset_y"],
                    "action_type": step["action_type"],
                    "key_to_press": step["key_to_press"],
                    "template_name": step["template_name"],
                    "step_interval": step.get("step_interval", 1.0)  # Save individual step interval
                }
                workflow_data["steps"].append(step_data)

            with open(filepath, 'w') as f:
                json.dump(workflow_data, f, indent=4)
            print(f"Autosave: Workflow saved to {filepath}")

        except Exception as e:
            print(f"Autosave Error: {e}")

    def _autosave_load(self):
        filepath = self.autosave_filepath
        if not os.path.exists(filepath):
            self.update_status("No autosave file found. Starting fresh.")
            return

        # Re-use the load_workflow logic but with a fixed path
        base_dir = os.path.dirname(os.path.abspath(filepath))
        try:
            with open(filepath, 'r') as f:
                workflow_data = json.load(f)

            self.clear_script()
            self.vid_entry.delete(0, tk.END)
            self.vid_entry.insert(0, workflow_data.get("vid", "046D"))
            self.pid_entry.delete(0, tk.END)
            self.pid_entry.insert(0, workflow_data.get("pid", "C534"))

            for step_data in workflow_data.get("steps", []):
                template_path = os.path.join(base_dir, step_data["template_path"])
                if not os.path.exists(template_path):
                    continue
                template_image_cv = cv2.imread(template_path)
                if template_image_cv is None:
                    continue
                step_details = {
                    "template_image": template_image_cv,
                    "offset_x": step_data["offset_x"],
                    "offset_y": step_data["offset_y"],
                    "action_type": step_data["action_type"],
                    "key_to_press": step_data["key_to_press"],
                    "template_name": step_data["template_name"],
                    "step_interval": step_data.get("step_interval", 1.0)  # Load individual step interval
                }
                self.script_steps.append(step_details)
                self.script_text_area.config(state="normal")
                script_line = f"Step {len(self.script_steps)}: Find '{step_details['template_name']}', Offset({step_details['offset_x']},{step_details['offset_y']}), Action: {step_details['action_type']}"
                if step_details['action_type'] == "Press Key":
                    script_line += f" [{step_details['key_to_press']}]"
                script_line += f", Wait: {step_details['step_interval']}s"
                self.script_text_area.insert(tk.END, script_line + "\n")
                self.script_text_area.config(state="disabled")

            self.update_status(f"Autosaved workflow loaded from {filepath}")
        except Exception as e:
            self.update_status(f"Could not load autosave file: {e}")

    def on_action_type_change(self, event=None):
        selected_action = self.action_type_combo.get()
        if selected_action == "Press Key":
            self.key_press_entry.config(state="normal")
        else:
            self.key_press_entry.config(state="disabled")

    def update_status(self, message, status_type="info"):
        """Update status bar with modern status indicators"""
        status_icons = {
            "info": "🔵",
            "success": "🟢",
            "warning": "🟡",
            "error": "🔴",
            "working": "🟠"
        }
        icon = status_icons.get(status_type, "🔵")
        self.status_bar.config(text=f"{icon} 状态：{message}")
        self.root.update_idletasks()

    def capture_template(self):
        # Check if PIL is available
        if not PIL_AVAILABLE:
            messagebox.showerror("功能不可用",
                               "模板捕获功能需要 PIL 库支持。\n\n请安装 PIL 库：\npip install pillow")
            self.update_status("模板捕获功能不可用 - 缺少 PIL 库", "error")
            return

        try:
            self.update_status("准备捕获模板...", "working")
            # Simple delay to allow user to prepare the screen.
            # For a better UX, a small countdown window or a hotkey would be ideal.
            self.update_status("准备区域选择...", "working")
            self.root.iconify() # Minimize main window

            # Using a short delay to ensure main window is minimized before overlay appears
            self.root.after(100, self.start_region_selection)

        except Exception as e:
            messagebox.showerror("捕获错误", f"启动捕获失败：{e}")
            self.update_status(f"启动捕获错误：{e}", "error")
            self.root.deiconify()

    def start_region_selection(self):
        try:
            self.selector = RegionSelector(self.root, self._on_region_selected)
        except Exception as e:
            messagebox.showerror("Capture Error", f"Failed to start region selector: {e}")
            self.update_status(f"Error starting region selector: {e}")
            self.root.deiconify()

    def _on_region_selected(self, bbox):
        self.root.deiconify() # Restore main window
        if bbox:
            try:
                self.update_status(f"Region selected: {bbox}. Capturing...")
                #PIL's ImageGrab.grab() bbox is (x1, y1, x2, y2)
                # Ensure coordinates are ordered correctly (x1 < x2, y1 < y2)
                x1, y1, x2, y2 = bbox
                if x1 > x2: x1, x2 = x2, x1
                if y1 > y2: y1, y2 = y2, y1

                # Ensure width and height are positive
                if x1 == x2 or y1 == y2:
                    messagebox.showerror("Capture Error", "Invalid region selected (zero width or height).")
                    self.update_status("Error: Invalid region selected.")
                    return

                captured_region_pil = ImageGrab.grab(bbox=(x1, y1, x2, y2), all_screens=True)

                self.template_image = cv2.cvtColor(np.array(captured_region_pil), cv2.COLOR_RGB2BGR)
                self.template_label.config(text=f"Template captured ({self.template_image.shape[1]}x{self.template_image.shape[0]})")
                self.update_status("Template captured successfully from selected region.")

                # Display thumbnail
                thumb_pil = captured_region_pil.copy()
                thumb_pil.thumbnail((200, 150))
                self.template_tk_image = ImageTk.PhotoImage(thumb_pil)
                self.template_display.config(image=self.template_tk_image)

            except Exception as e:
                messagebox.showerror("Capture Error", f"Failed to capture selected region: {e}")
                self.update_status(f"Error capturing region: {e}")
        else:
            self.update_status("Template capture cancelled or failed.")

        if hasattr(self, 'selector') and self.selector:
            # Ensure selector resources are cleaned up if it didn't close itself
            if hasattr(self.selector, 'overlay') and self.selector.overlay.winfo_exists(): # Check if overlay exists before destroying
                self.selector.overlay.destroy()
            self.selector = None

    def load_template_from_file(self):
        # Check if required libraries are available
        if not CV2_AVAILABLE:
            messagebox.showerror("功能不可用",
                               "模板加载功能需要 OpenCV 库支持。\n\n请安装 OpenCV 库：\npip install opencv-python")
            self.update_status("模板加载功能不可用 - 缺少 OpenCV 库", "error")
            return

        if not PIL_AVAILABLE:
            messagebox.showerror("功能不可用",
                               "模板显示功能需要 PIL 库支持。\n\n请安装 PIL 库：\npip install pillow")
            self.update_status("模板显示功能不可用 - 缺少 PIL 库", "error")
            return

        try:
            filepath = filedialog.askopenfilename(
                title="选择模板图像",
                filetypes=(("PNG files", "*.png"), ("JPEG files", "*.jpg;*.jpeg"), ("All files", "*.*"))
            )
            if not filepath:
                self.update_status("模板加载已取消", "info")
                return

            self.update_status(f"正在加载模板：{filepath}...", "working")
            # Read with OpenCV, store in BGR format
            loaded_image_cv = cv2.imread(filepath)
            if loaded_image_cv is None:
                messagebox.showerror("加载错误", "无法读取图像文件。请确保文件是 OpenCV 支持的有效图像格式。")
                self.update_status(f"错误：无法读取图像文件 {filepath}", "error")
                return

            self.template_image = loaded_image_cv
            self.template_label.config(text=f"模板已加载 ({self.template_image.shape[1]}x{self.template_image.shape[0]})")
            self.update_status("模板文件加载成功", "success")

            # Display thumbnail using Pillow to open for PhotoImage compatibility
            pil_image = Image.open(filepath)
            pil_image.thumbnail((200, 150)) # Max width 200, max height 150
            self.template_tk_image = ImageTk.PhotoImage(pil_image)
            self.template_display.config(image=self.template_tk_image)

        except Exception as e:
            messagebox.showerror("加载错误", f"加载模板失败：{e}")
            self.update_status(f"加载模板错误：{e}", "error")

    def toggle_automation(self):
        if self.is_automating:
            self.stop_automation()
        else:
            self.start_automation()

    def start_automation(self):
        # Check if required libraries are available
        if not CV2_AVAILABLE or not PIL_AVAILABLE:
            missing_libs = []
            if not CV2_AVAILABLE:
                missing_libs.append("OpenCV (pip install opencv-python)")
            if not PIL_AVAILABLE:
                missing_libs.append("PIL (pip install pillow)")

            messagebox.showerror("功能不可用",
                               f"自动化功能需要以下库支持：\n\n" + "\n".join(missing_libs))
            self.update_status("自动化功能不可用 - 缺少必要库", "error")
            return

        if self.template_image is None: # Check if template_image is None
            messagebox.showerror("错误", "请先捕获或加载模板图像。")
            return

        vid_str = self.vid_entry.get()
        pid_str = self.pid_entry.get()
        try:
            interval = float(self.interval_entry.get())
            if interval <= 0:
                raise ValueError("Interval must be positive.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid interval: {e}")
            return

        if not vid_str or not pid_str:
            messagebox.showerror("Input Error", "VID and PID cannot be empty.")
            return

        try:
            # Convert hex string VID/PID from UI to integers
            vid_int = int(vid_str, 16)
            pid_int = int(pid_str, 16)
        except ValueError:
            messagebox.showerror("Input Error", "VID and PID must be valid hexadecimal numbers (e.g., 046D).")
            return

        try:
            self.update_status(f"Initializing mouse controller (VID:{vid_str}, PID:{pid_str})...")
            if not self.mouse_controller: # Create instance if it doesn't exist
                self.mouse_controller = ImplementationA(vid=vid_int, pid=pid_int)

            self.update_status("ImplementationA object available. Initializing device...")
            # wyhz_init now stores the handle internally in mouse_controller
            # and also returns it. We might not need to store it separately in wyUsbHandle.
            # For now, we assume mouse_controller.wyhz_init() will raise error on failure
            # or mouse_controller.hkm_handle will be None.
            returned_handle = self.mouse_controller.wyhz_init()

            if not self.mouse_controller.hkm_handle or not returned_handle: # Check internal handle
                messagebox.showerror("Device Error", "Failed to initialize USB device or get handle (hkm_handle is None).")
                self.update_status("Error: Failed to initialize USB device via mouse_controller.")
                # self.mouse_controller.close_device_handle() # Ensure cleanup if partially initialized
                self.mouse_controller = None # Or just ensure hkm_handle is None
                return
            self.wyUsbHandle = returned_handle # Still store raw handle if needed elsewhere, though actions go via mouse_controller
            self.update_status("Mouse controller initialized and USB handle obtained.")
        except Exception as e:
            messagebox.showerror("Device Error", f"Failed to initialize mouse controller: {e}")
            self.update_status(f"Error initializing mouse controller: {e}")
            if self.mouse_controller:
                self.mouse_controller.close_device_handle() # Attempt to clean up
            self.mouse_controller = None
            self.wyUsbHandle = None # Ensure this is also cleared
            return

        self.is_automating = True
        self.start_stop_button.config(text="Stop Automation")
        self.update_status("Automation started. UI will minimize.")

        # Minimize the main window
        # Adding a short delay before starting the thread to allow UI to update/minimize
        self.root.after(100, self._start_automation_thread, interval)

    def _start_automation_thread(self, interval):
        # self.root.iconify() # Minimize UI # Keep UI open for workflow
        self.automation_thread = threading.Thread(target=self.automation_loop, args=(interval,), daemon=True)
        self.automation_thread.start()

    def stop_automation(self):
        self.is_automating = False
        if self.automation_thread and self.automation_thread.is_alive():
             # Thread will exit due to self.is_automating flag
             # Optionally, one could join the thread if synchronous stop is critical,
             # but for UI responsiveness, letting it exit via flag is usually fine.
             # self.automation_thread.join(timeout=interval*1.5) # Example if join is needed
             pass

        # Restore the main window
        if self.root.state() == 'iconic':
            self.root.deiconify()

        self.start_stop_button.config(text="Start Automation")
        self.update_status("Automation stopped. UI restored.")

        if self.mouse_controller and self.mouse_controller.hkm_handle: # Check internal handle
            try:
                # close_device_handle in ImplementationA now uses its internal handle
                self.mouse_controller.close_device_handle()
                self.update_status("USB device handle released by mouse_controller.")
            except Exception as e:
                self.update_status(f"Error releasing USB device handle via mouse_controller: {e}")

        # If workflow is not also trying to clean up, then nullify.
        # This logic might need refinement if both single auto and workflow manage same controller instance.
        if not self.is_workflow_running: # Only nullify if workflow isn't also using it
            self.mouse_controller = None
            self.wyUsbHandle = None # wyUsbHandle is just a copy of the raw handle, clear it too.

    def automation_loop(self, interval):
        while self.is_automating:
            if not self.mouse_controller or not self.mouse_controller.hkm_handle:
                self.update_status("Error: Mouse controller not available/initialized. Stopping automation.")
                self.is_automating = False
                break
            if self.template_image is None:
                self.update_status("Error: Template image is missing. Stopping automation.")
                self.is_automating = False # Stop loop if template disappears
                break
            try:
                self.update_status("Searching for template...")
                screenshot_pil = ImageGrab.grab()
                current_screen_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                if self.template_image is None:
                    self.update_status("Critical Error: Template became None during loop. Stopping.")
                    self.is_automating = False
                    break

                result = cv2.matchTemplate(current_screen_cv, self.template_image, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                threshold = 0.8
                if max_val >= threshold:
                    template_h, template_w = self.template_image.shape[:2]
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2

                    # Update UI with found coordinates and confidence (thread-safe)
                    def _update_gui_found_info():
                        self.found_x_entry.config(state="normal")
                        self.found_y_entry.config(state="normal")
                        self.confidence_entry.config(state="normal")

                        self.found_x_entry.delete(0, tk.END)
                        self.found_x_entry.insert(0, str(max_loc[0]))
                        self.found_y_entry.delete(0, tk.END)
                        self.found_y_entry.insert(0, str(max_loc[1]))
                        self.confidence_entry.delete(0, tk.END)
                        self.confidence_entry.insert(0, f"{max_val:.2f}")

                        self.found_x_entry.config(state="readonly")
                        self.found_y_entry.config(state="readonly")
                        self.confidence_entry.config(state="readonly")

                    self.root.after(0, _update_gui_found_info)


                    self.update_status(f"Template found at ({max_loc[0]}, {max_loc[1]}) with conf {max_val:.2f}. Processing action.")

                    if self.mouse_controller and self.mouse_controller.hkm_handle:
                        # Get offset values
                        try:
                            offset_x = int(self.offset_x_entry.get())
                        except ValueError:
                            offset_x = 0
                        try:
                            offset_y = int(self.offset_y_entry.get())
                        except ValueError:
                            offset_y = 0

                        target_x = center_x + offset_x
                        target_y = center_y + offset_y

                        click_x_int = int(round(target_x))
                        click_y_int = int(round(target_y)) # Use target_y

                        action_type = self.action_type_combo.get()
                        key_to_press = self.key_press_entry.get()

                        self.mouse_controller.MoveTo(click_x_int, click_y_int)
                        time.sleep(0.05) # Small delay after move, before action

                        if action_type == "Left Click":
                            self.mouse_controller.LeftClick()
                            self.update_status(f"Left clicked at ({click_x_int}, {click_y_int}).")
                        elif action_type == "Right Click":
                            try:
                                self.mouse_controller.RightClick()
                                self.update_status(f"Right clicked at ({click_x_int}, {click_y_int}).")
                            except NotImplementedError as e:
                                self.update_status(f"Error: RightClick not implemented in controller: {e}")
                                print(f"DEBUG: RightClick failed: {e}")
                            except Exception as e:
                                self.update_status(f"Error during RightClick: {e}")
                                print(f"DEBUG: RightClick runtime error: {e}")
                        elif action_type == "Double Click":
                            self.mouse_controller.LeftClick()
                            time.sleep(0.08) # Delay between clicks for double click
                            self.mouse_controller.LeftClick()
                            self.update_status(f"Double clicked at ({click_x_int}, {click_y_int}).")
                        elif action_type == "Press Key":
                            if key_to_press:
                                try:
                                    self.mouse_controller.PressKey(key_to_press)
                                    self.update_status(f"Pressed key '{key_to_press}' at ({click_x_int}, {click_y_int}).")
                                except NotImplementedError as e:
                                    self.update_status(f"Error: PressKey not implemented in controller: {e}")
                                    print(f"DEBUG: PressKey failed: {e}")
                                except ValueError as e: # For unknown key string
                                    self.update_status(f"Error: Invalid key for PressKey: {e}")
                                    print(f"DEBUG: PressKey invalid key: {e}")
                                except Exception as e:
                                    self.update_status(f"Error during PressKey: {e}")
                                    print(f"DEBUG: PressKey runtime error: {e}")
                            else:
                                self.update_status("Warning: 'Press Key' selected but no key specified.")

                    else:
                        self.update_status("Error: Mouse controller not available for action.")
                        # print(f"DEBUG: wyUsbHandle not available. Would act at ({target_x}, {target_y}) with action {action_type}")
                        print(f"DEBUG: Mouse controller not available. Would act at ({target_x}, {target_y}) with action {action_type}")
                else:
                    self.update_status(f"Template not found (max conf: {max_val:.2f} < {threshold}).")

            except Exception as e:
                print(f"Error in automation loop: {e}")
                self.update_status(f"Error: {e}")

            for _ in range(int(interval * 10)):
                if not self.is_automating:
                    break
                time.sleep(0.1)

        self.update_status("Automation loop finished.")

    # --- Scripting Methods ---
    def add_action_to_script(self):
        if self.template_image is None:
            messagebox.showwarning("脚本编辑", "请先捕获或加载模板图像以添加动作。")
            return

        # For simplicity, we'll store the template image itself (as np.array) in the script step.
        # For larger scripts or if saving/loading scripts to files is desired,
        # storing template file paths would be more appropriate.
        current_template_cv = self.template_image.copy() # Store a copy

        try:
            offset_x = int(self.offset_x_entry.get())
        except ValueError:
            offset_x = 0
        try:
            offset_y = int(self.offset_y_entry.get())
        except ValueError:
            offset_y = 0

        action_type = self.action_type_combo.get()
        key_to_press = self.key_press_entry.get() if action_type == "Press Key" else ""

        # Get step interval
        try:
            step_interval = float(self.step_interval_entry.get())
            if step_interval < 0:
                step_interval = 0.0
        except ValueError:
            step_interval = 1.0  # Default value

        step_details = {
            "template_image": current_template_cv,
            "offset_x": offset_x,
            "offset_y": offset_y,
            "action_type": action_type,
            "key_to_press": key_to_press,
            "template_name": self.template_label.cget("text"), # Get text from template_label
            "step_interval": step_interval  # Add individual step interval
        }
        self.script_steps.append(step_details)

        # Update script text area with enhanced formatting
        self.script_text_area.config(state="normal")

        # Hide empty script label if this is the first step
        if len(self.script_steps) == 1:
            self.empty_script_label.place_forget()

        # Create formatted script line with icons
        action_icons = {
            "Left Click": "🖱️",
            "Right Click": "🖱️",
            "Double Click": "🖱️",
            "Press Key": "⌨️"
        }
        icon = action_icons.get(action_type, "⚡")

        script_line = f"📍 步骤 {len(self.script_steps)}: {icon} 查找 '{step_details['template_name']}'"
        script_line += f"\n   📐 偏移：({offset_x}, {offset_y})"
        script_line += f" | 🎯 动作：{action_type}"
        if action_type == "Press Key":
            script_line += f" [{key_to_press}]"
        script_line += f" | ⏱️ 等待：{step_interval}s\n"

        self.script_text_area.insert(tk.END, script_line + "\n")
        self.script_text_area.config(state="disabled")

        # Update step counter
        self.step_counter_label.config(text=f"步骤数量：{len(self.script_steps)}")

        self.update_status(f"动作已添加到脚本。总步骤数：{len(self.script_steps)}", "success")

    def run_workflow(self):
        # Check if required libraries are available
        if not CV2_AVAILABLE or not PIL_AVAILABLE:
            missing_libs = []
            if not CV2_AVAILABLE:
                missing_libs.append("OpenCV (pip install opencv-python)")
            if not PIL_AVAILABLE:
                missing_libs.append("PIL (pip install pillow)")

            messagebox.showerror("功能不可用",
                               f"工作流功能需要以下库支持：\n\n" + "\n".join(missing_libs))
            self.update_status("工作流功能不可用 - 缺少必要库", "error")
            return

        if not self.script_steps:
            messagebox.showinfo("工作流", "脚本为空。请先添加动作。")
            return
        if self.is_automating:
            messagebox.showwarning("工作流", "无法在单次自动化运行时启动工作流。请先停止自动化。")
            return
        if self.is_workflow_running:
            messagebox.showwarning("工作流", "工作流已在运行中。")
            return

        try:
            runs = int(self.workflow_runs_entry.get())
            if runs < 0:
                raise ValueError("Execution count cannot be negative.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid execution count: {e}. Please enter a non-negative integer.")
            return

        try:
            step_interval = float(self.workflow_interval_entry.get())
            if step_interval < 0:
                raise ValueError("Step interval cannot be negative.")
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid step interval: {e}. Please enter a non-negative number.")
            return

        vid_str = self.vid_entry.get()
        pid_str = self.pid_entry.get()
        if not vid_str or not pid_str:
            messagebox.showerror("Input Error", "VID and PID cannot be empty for workflow execution.")
            return
        try:
            vid_int = int(vid_str, 16)
            pid_int = int(pid_str, 16)
        except ValueError:
            messagebox.showerror("Input Error", "VID and PID must be valid hexadecimal numbers for workflow.")
            return

        try:
            if not self.mouse_controller or not self.mouse_controller.hkm_handle:
                self.update_status(f"Workflow: Initializing mouse controller (VID:{vid_str}, PID:{pid_str})...")
                if not self.mouse_controller:
                    self.mouse_controller = ImplementationA(vid=vid_int, pid=pid_int)
                returned_handle = self.mouse_controller.wyhz_init()
                if not self.mouse_controller.hkm_handle or not returned_handle:
                    messagebox.showerror("Device Error", "Workflow: Failed to initialize USB device or get handle.")
                    self.update_status("Workflow Error: Failed to initialize USB device.")
                    if self.mouse_controller: self.mouse_controller.close_device_handle()
                    self.mouse_controller = None
                    self.wyUsbHandle = None
                    return
                self.wyUsbHandle = returned_handle
                self.update_status("Workflow: Mouse controller initialized.")
            else:
                self.update_status("Workflow: Using existing initialized mouse controller.")
        except Exception as e:
            messagebox.showerror("Device Error", f"Workflow: Failed to initialize mouse controller: {e}")
            self.update_status(f"Workflow Error: Init controller: {e}")
            if self.mouse_controller: self.mouse_controller.close_device_handle()
            self.mouse_controller = None
            self.wyUsbHandle = None
            return

        self.is_workflow_running = True
        self.run_workflow_button.config(state="disabled")
        self.add_action_button.config(state="disabled")
        self.clear_script_button.config(state="disabled")
        self.start_stop_button.config(state="disabled")
        self.stop_workflow_button.config(state="normal")
        self.update_status("Workflow started...")

        self.workflow_thread = threading.Thread(target=self.workflow_execution_manager, args=(runs, step_interval), daemon=True)
        self.workflow_thread.start()

    def workflow_execution_manager(self, total_runs, step_interval=0.5):
        run_count = 0
        is_infinite = (total_runs == 0)

        while self.is_workflow_running and (is_infinite or run_count < total_runs):
            if not self.is_workflow_running:
                break

            run_count += 1
            if is_infinite:
                self.update_status(f"Starting workflow execution cycle {run_count}...")
            else:
                self.update_status(f"Starting workflow execution {run_count}/{total_runs}...")

            # Execute one full workflow cycle
            self.workflow_loop(step_interval)

            # If the workflow was stopped during the loop, exit the manager loop
            if not self.is_workflow_running:
                break

            # Small delay between full workflow runs, can be made configurable
            if is_infinite or run_count < total_runs:
                self.update_status(f"Cycle {run_count} finished. Waiting before next run...")
                for _ in range(10): # e.g., 1s delay, checking stop flag
                    if not self.is_workflow_running: break
                    time.sleep(0.1)

        # Workflow finished or stopped
        self.is_workflow_running = False
        self.root.after(0, self._finalize_workflow_gui)

    def workflow_loop(self, default_step_interval=0.5):
        for i, step in enumerate(self.script_steps):
            if not self.is_workflow_running:
                self.update_status("Workflow stopped by user.")
                break

            # Get individual step interval or use default
            step_interval = step.get("step_interval", default_step_interval)

            self.update_status(f"Workflow: Executing Step {i+1}/{len(self.script_steps)}: Find '{step['template_name']}'...")

            current_template_cv = step["template_image"]
            if current_template_cv is None:
                self.update_status(f"Workflow Error: Step {i+1} has no template image. Skipping.")
                print(f"DEBUG: Workflow Step {i+1} missing template_image.")
                time.sleep(step_interval) # Pause before next step
                continue

            try:
                screenshot_pil = ImageGrab.grab()
                current_screen_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                result = cv2.matchTemplate(current_screen_cv, current_template_cv, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                threshold = 0.8 # Consider making this configurable per step in future
                if max_val >= threshold:
                    template_h, template_w = current_template_cv.shape[:2]
                    center_x = max_loc[0] + template_w // 2
                    center_y = max_loc[1] + template_h // 2

                    target_x = center_x + step["offset_x"]
                    target_y = center_y + step["offset_y"]
                    click_x_int = int(round(target_x))
                    click_y_int = int(round(target_y))

                    action_description = f"Action: {step['action_type']}"
                    if step['action_type'] == "Press Key":
                        action_description += f" [{step['key_to_press']}]"
                    self.update_status(f"Step {i+1}: Found at ({max_loc[0]},{max_loc[1]}), Conf: {max_val:.2f}. {action_description} at ({click_x_int},{click_y_int}).")

                    if self.mouse_controller and self.mouse_controller.hkm_handle:
                        self.mouse_controller.MoveTo(click_x_int, click_y_int)
                        time.sleep(0.05) # Small delay after move

                        action_type = step["action_type"]
                        key_to_press = step["key_to_press"]

                        try:
                            if action_type == "Left Click":
                                self.mouse_controller.LeftClick()
                            elif action_type == "Right Click":
                                self.mouse_controller.RightClick()
                            elif action_type == "Double Click":
                                self.mouse_controller.LeftClick()
                                time.sleep(0.08)
                                self.mouse_controller.LeftClick()
                            elif action_type == "Press Key":
                                if key_to_press:
                                    self.mouse_controller.PressKey(key_to_press)
                                else:
                                    self.update_status(f"Step {i+1} Warning: No key for Press Key action.")
                        except NotImplementedError as e:
                            self.update_status(f"Step {i+1} Error: Action '{action_type}' not implemented: {e}")
                            print(f"DEBUG: Workflow Step {i+1} NotImplementedError: {e}")
                        except ValueError as e: # For invalid key in PressKey
                             self.update_status(f"Step {i+1} Error: Invalid param for '{action_type}': {e}")
                             print(f"DEBUG: Workflow Step {i+1} ValueError: {e}")
                        except Exception as e:
                            self.update_status(f"Step {i+1} Error during action '{action_type}': {e}")
                            print(f"DEBUG: Workflow Step {i+1} Exception: {e}")

                        # Use individual step interval for delay after action
                        if step_interval > 0:
                            self.update_status(f"Step {i+1} completed. Waiting {step_interval}s before next step...")
                            delay_steps = max(1, int(step_interval * 10))  # Convert to 0.1s increments
                            for _ in range(delay_steps):
                                if not self.is_workflow_running: break
                                time.sleep(0.1)
                    else:
                        self.update_status(f"Step {i+1} Error: Mouse controller not available.")
                        print(f"DEBUG: Workflow Step {i+1} - Mouse controller missing.")
                        break # Critical error, stop workflow
                else:
                    self.update_status(f"Step {i+1}: Template not found (max conf: {max_val:.2f} < {threshold}). Skipping action.")
                    if step_interval > 0:
                        time.sleep(step_interval) # Pause if template not found

            except Exception as e:
                print(f"Error in workflow step {i+1}: {e}")
                self.update_status(f"Workflow Error Step {i+1}: {e}")
                if step_interval > 0:
                    time.sleep(step_interval) # Pause on error

        # This loop is now one cycle of the workflow.
        # The manager function will handle repetition and finalization.
        if not self.is_workflow_running:
             self.update_status("Workflow stopped during execution.")
        else:
             self.update_status("Workflow cycle finished.")

    def _finalize_workflow_gui(self):
        self.run_workflow_button.config(state="normal")
        self.add_action_button.config(state="normal")
        self.clear_script_button.config(state="normal")
        self.start_stop_button.config(state="normal")
        self.stop_workflow_button.config(state="disabled")

        # Decide if we close the handle here or keep it open if user might run again soon.
        # For now, let's close it to be safe.
        if self.mouse_controller and self.mouse_controller.hkm_handle: # Check internal handle
            try:
                # Check if single automation is also trying to use it.
                # If single automation is running, it will manage its own handle closure.
                if not self.is_automating:
                    self.mouse_controller.close_device_handle() # Uses internal handle
                    self.update_status("Workflow finished. USB device handle released by mouse_controller.")
                    self.mouse_controller = None # Nullify controller if we closed it
                    self.wyUsbHandle = None      # Nullify raw handle copy
                else:
                    # If single automation is running, it means it likely initialized the controller.
                    # Workflow should not close it in this case.
                    self.update_status("Workflow finished. Single automation is active, USB handle retained by it.")
            except Exception as e:
                self.update_status(f"Workflow: Error releasing USB handle via mouse_controller: {e}")
        else:
             self.update_status("Workflow finished.")

        # If mouse_controller became None above (because we closed it), self.wyUsbHandle should also be None.
        if not self.mouse_controller:
            self.wyUsbHandle = None


    def stop_workflow(self):
        if self.is_workflow_running:
            self.is_workflow_running = False # Signal the loop to stop
            # Thread will call _finalize_workflow_gui once it exits
            self.update_status("Stopping workflow...")
        else:
            self.update_status("Workflow is not running.")
            # Ensure GUI is reset if somehow stop is called when not running
            self._finalize_workflow_gui()


    def clear_script(self):
        if self.is_workflow_running:
            messagebox.showwarning("Scripting", "Cannot clear script while workflow is running.")
            return
        self.script_steps = []
        self.script_text_area.config(state="normal")
        self.script_text_area.delete(1.0, tk.END)
        self.script_text_area.config(state="disabled")

        # Show empty script label
        self.empty_script_label.place(relx=0.5, rely=0.5, anchor=tk.CENTER)

        # Update step counter
        self.step_counter_label.config(text="步骤数量：0")

        self.update_status("脚本已清空", "info")

    # --- Scrolling Methods ---
    def _on_frame_configure(self, event=None):
        """Reset the scroll region to encompass the inner frame"""
        self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))

    def _on_canvas_configure(self, event=None):
        """Reset the canvas window to encompass the inner frame"""
        if event:
            canvas_width = event.width
            self.main_canvas.itemconfig(self.canvas_frame_id, width=canvas_width)

    def _on_mouse_wheel(self, event):
        """Process mouse wheel events for scrolling"""
        # The delta value is different on Windows vs. Linux/macOS
        if platform.system() == "Windows":
            self.main_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        else: # For Linux and macOS
            if event.num == 5: # Scroll down
                self.main_canvas.yview_scroll(1, "units")
            elif event.num == 4: # Scroll up
                self.main_canvas.yview_scroll(-1, "units")

    def save_workflow(self):
        if not self.script_steps:
            messagebox.showinfo("Save Workflow", "The script is empty. Nothing to save.")
            return

        filepath = filedialog.asksaveasfilename(
            title="Save Workflow File",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
        if not filepath:
            self.update_status("Save workflow cancelled.")
            return

        # Create a directory for assets next to the JSON file
        base_dir = os.path.dirname(filepath)
        file_name = os.path.basename(filepath)
        asset_dir_name = os.path.splitext(file_name)[0] + "_assets"
        asset_path = os.path.join(base_dir, asset_dir_name)

        try:
            if not os.path.exists(asset_path):
                os.makedirs(asset_path)
            self.update_status(f"Created asset directory: {asset_path}")
        except OSError as e:
            messagebox.showerror("Save Error", f"Failed to create asset directory: {e}")
            self.update_status(f"Error creating asset directory: {e}")
            return

        workflow_data = {
            "vid": self.vid_entry.get(),
            "pid": self.pid_entry.get(),
            "steps": []
        }

        try:
            for i, step in enumerate(self.script_steps):
                template_filename = f"step_{i+1}_template.png"
                template_filepath = os.path.join(asset_path, template_filename)

                # Save the numpy array (template_image) as a PNG file
                if step["template_image"] is not None:
                    success = cv2.imwrite(template_filepath, step["template_image"])
                    if not success:
                        raise Exception(f"Failed to save template image for step {i+1}")
                    self.update_status(f"Saved template image: {template_filepath}")
                else:
                    raise Exception(f"Step {i+1} has no template image to save")

                step_data = {
                    "template_path": os.path.join(asset_dir_name, template_filename), # Relative path
                    "offset_x": step["offset_x"],
                    "offset_y": step["offset_y"],
                    "action_type": step["action_type"],
                    "key_to_press": step["key_to_press"],
                    "template_name": step["template_name"],
                    "step_interval": step.get("step_interval", 1.0)  # Save individual step interval
                }
                workflow_data["steps"].append(step_data)

            with open(filepath, 'w') as f:
                json.dump(workflow_data, f, indent=4)

            self.update_status(f"Workflow saved successfully to {filepath}")
            messagebox.showinfo("Save Workflow", "Workflow and all template images have been saved.")

        except Exception as e:
            messagebox.showerror("Save Error", f"An error occurred while saving the workflow: {e}")
            self.update_status(f"Error saving workflow: {e}")

    def load_workflow(self):
        if self.is_workflow_running:
            messagebox.showwarning("Load Workflow", "Cannot load a new workflow while one is running.")
            return

        filepath = filedialog.askopenfilename(
            title="Load Workflow File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
        if not filepath:
            self.update_status("Load workflow cancelled.")
            return

        base_dir = os.path.dirname(filepath)

        try:
            with open(filepath, 'r') as f:
                workflow_data = json.load(f)

            # Clear existing script first
            self.clear_script()

            # Load VID/PID
            self.vid_entry.delete(0, tk.END)
            self.vid_entry.insert(0, workflow_data.get("vid", "046D"))
            self.pid_entry.delete(0, tk.END)
            self.pid_entry.insert(0, workflow_data.get("pid", "C534"))

            for step_data in workflow_data.get("steps", []):
                template_path = os.path.join(base_dir, step_data["template_path"])
                self.update_status(f"Loading template: {template_path}")

                if not os.path.exists(template_path):
                    error_msg = f"Template image not found: {template_path}. Skipping this step."
                    messagebox.showwarning("Load Warning", error_msg)
                    self.update_status(f"Warning: {error_msg}")
                    continue

                # Load template image with OpenCV
                template_image_cv = cv2.imread(template_path)
                if template_image_cv is None:
                    error_msg = f"Failed to load template image: {template_path}. Skipping this step."
                    messagebox.showwarning("Load Warning", error_msg)
                    self.update_status(f"Warning: {error_msg}")
                    continue

                self.update_status(f"Successfully loaded template: {template_path}")

                step_details = {
                    "template_image": template_image_cv,
                    "offset_x": step_data["offset_x"],
                    "offset_y": step_data["offset_y"],
                    "action_type": step_data["action_type"],
                    "key_to_press": step_data["key_to_press"],
                    "template_name": step_data["template_name"],
                    "step_interval": step_data.get("step_interval", 1.0)  # Load individual step interval
                }
                self.script_steps.append(step_details)

                # Update the UI text area
                self.script_text_area.config(state="normal")
                script_line = f"Step {len(self.script_steps)}: Find '{step_details['template_name']}', Offset({step_details['offset_x']},{step_details['offset_y']}), Action: {step_details['action_type']}"
                if step_details['action_type'] == "Press Key":
                    script_line += f" [{step_details['key_to_press']}]"
                script_line += f", Wait: {step_details['step_interval']}s"
                self.script_text_area.insert(tk.END, script_line + "\n")
                self.script_text_area.config(state="disabled")

            self.update_status(f"Workflow loaded successfully from {filepath}")
            messagebox.showinfo("Load Workflow", "Workflow loaded successfully.")

        except json.JSONDecodeError:
            messagebox.showerror("Load Error", "Invalid JSON file. The workflow file may be corrupted.")
            self.update_status("Error: Invalid JSON file.")
        except KeyError as e:
            messagebox.showerror("Load Error", f"Missing expected data in workflow file: {e}. The file might be from an incompatible version.")
            self.update_status(f"Error: Missing data in workflow file: {e}")
        except Exception as e:
            messagebox.showerror("Load Error", f"An error occurred while loading the workflow: {e}")
            self.update_status(f"Error loading workflow: {e}")


class RegionSelector:
    def __init__(self, parent_root, callback):
        self.parent_root = parent_root # Main app root, used for positioning if needed
        self.callback = callback
        self.start_x = None
        self.start_y = None
        self.current_x = None
        self.current_y = None
        self.rect = None

        # Create a top-level window for the overlay
        self.overlay = tk.Toplevel(self.parent_root)
        self.overlay.attributes("-fullscreen", True) # Make it fullscreen
        self.overlay.attributes("-alpha", 0.3)  # Make it semi-transparent (0.0 fully transparent, 1.0 opaque)
        self.overlay.attributes("-topmost", True) # Keep it on top
        self.overlay.overrideredirect(True) # Remove window decorations (title bar, borders)

        # A canvas on the overlay window to draw the selection rectangle
        self.canvas = tk.Canvas(self.overlay, cursor="crosshair", bg="grey") # Grey bg for visibility of transparency
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Add instructional text
        # Get screen width and height for text placement
        screen_width = self.overlay.winfo_screenwidth()
        screen_height = self.overlay.winfo_screenheight()

        instruction_text = "Click and drag to select a region. Press Esc to cancel."
        # Simple text shadow for better readability on varying backgrounds
        self.canvas.create_text(screen_width / 2 + 1, screen_height / 2 + 1, text=instruction_text, font=("Arial", 16), fill="black")
        self.canvas.create_text(screen_width / 2, screen_height / 2, text=instruction_text, font=("Arial", 16), fill="white")

        # Bind mouse events
        self.canvas.bind("<ButtonPress-1>", self.on_mouse_press)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_release)

        # Allow cancellation with Escape key
        self.overlay.bind("<Escape>", self.cancel_selection)

        # For some systems, focus might be needed
        self.overlay.focus_force()


    def on_mouse_press(self, event):
        self.start_x = self.canvas.canvasx(event.x)
        self.start_y = self.canvas.canvasy(event.y)
        if self.rect:
            self.canvas.delete(self.rect)
        self.rect = None

    def on_mouse_drag(self, event):
        self.current_x = self.canvas.canvasx(event.x)
        self.current_y = self.canvas.canvasy(event.y)
        if self.rect:
            self.canvas.delete(self.rect)
        self.rect = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.current_x, self.current_y,
            outline='red', width=2
        )

    def on_mouse_release(self, event):
        if self.start_x is not None and self.start_y is not None and \
           self.current_x is not None and self.current_y is not None:
            # Get final coordinates relative to the screen
            # canvasx/canvasy give coordinates relative to canvas, which is fine as it's fullscreen
            x1 = min(self.start_x, self.current_x)
            y1 = min(self.start_y, self.current_y)
            x2 = max(self.start_x, self.current_x)
            y2 = max(self.start_y, self.current_y)

            # Adjust for the canvas being on the overlay window.
            # The coordinates from canvas should be screen coordinates as it's fullscreen.
            # However, ImageGrab.grab(bbox=...) expects screen coordinates.
            # We need to ensure these are truly screen coordinates.
            # For a simple fullscreen overlay, canvas coordinates should map directly.
            # If multi-monitor or specific window managers cause issues, this might need adjustment.
            # For now, assume direct mapping.

            bbox = (int(x1), int(y1), int(x2), int(y2))
            self.overlay.destroy() # Close the overlay window
            self.callback(bbox)
        else: # Click without drag, or some other issue
            self.overlay.destroy()
            self.callback(None)

    def cancel_selection(self, event=None):
        if self.overlay.winfo_exists():
            self.overlay.destroy()
        self.callback(None) # Signal cancellation






if __name__ == "__main__":
    try:
        if platform.system() == "Windows":
            # Try to set Per Monitor V2 DPI awareness
            # Process per monitor DPI aware
            # (2) PROCESS_PER_MONITOR_DPI_AWARE = Per monitor V2
            # (1) PROCESS_SYSTEM_DPI_AWARE = System DPI aware
            # (0) PROCESS_DPI_UNAWARE = DPI Unaware
            ctypes.windll.shcore.SetProcessDpiAwareness(2)
            print("Attempted to set Per Monitor V2 DPI awareness.")
    except AttributeError:
        # If shcore.SetProcessDpiAwareness is not found (e.g., older Windows versions)
        # Try setting system DPI awareness as a fallback
        try:
            if platform.system() == "Windows":
                ctypes.windll.user32.SetProcessDPIAware()
                print("Attempted to set System DPI awareness (fallback).")
        except AttributeError:
            print("Could not set DPI awareness (SetProcessDPIAware not found).")
        except Exception as e:
            print(f"Error setting DPI awareness (fallback): {e}")
    except Exception as e:
        print(f"Error setting Per Monitor V2 DPI awareness: {e}")

    root = tk.Tk()
    app = AutomationApp(root)
    root.mainloop()
